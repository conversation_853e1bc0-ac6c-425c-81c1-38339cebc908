# Hybrid CommonJS/ESM Architecture for VSCode Extension

This document describes the hybrid CommonJS/ESM architecture implemented to support @xenova/transformers in the VSCode extension while maintaining compatibility with VSCode's extension host.

## Problem

VSCode extensions must use CommonJS format for compatibility with the extension host, but @xenova/transformers strictly requires ES modules. This created a conflict that prevented the extension from using the transformers library.

## Solution

Implemented a hybrid architecture where:
1. **Main extension** continues to use CommonJS format for VSCode compatibility
2. **Worker processes** use ES modules to support @xenova/transformers
3. **Communication** between CommonJS extension and ESM workers via IPC

## Changes Made

### 1. Build Configuration (`esbuild.config.js`)

- **Split build into two configurations:**
  - `extensionConfig`: Builds main extension as CommonJS
  - `workersConfig`: Builds workers as ES modules
- **Added external dependencies** to prevent bundling of native modules
- **Added ES module banner** to workers for Node.js compatibility
- **Automated package.json creation** for workers directory

### 2. Worker Directory Structure

```
src/workers/
├── package.json          # {"type": "module"} for ESM
├── tsconfig.json         # ESM-specific TypeScript config
├── utils/
│   └── initializeEmbeddingModel.ts  # ESM version of utility
├── codebase/
│   ├── codebaseSearchWorker.ts
│   └── codebaseEmbeddingProcess.ts
└── confluence/
    ├── searchProcess.ts
    └── createEmbeddingForText.ts
```

### 3. Updated Worker Files

- **Removed dynamic import workaround** (`new Function('modulePath', 'return import(modulePath)')`)
- **Added direct ES module imports** (`import { pipeline } from '@xenova/transformers'`)
- **Created ESM version** of `initializeEmbeddingModel` utility
- **Updated import paths** to use local ESM utilities

### 4. TypeScript Configuration

- **Main tsconfig.json**: Remains CommonJS for extension compatibility
- **Workers tsconfig.json**: Configured for ES modules with proper module resolution

## Architecture Overview

```
┌─────────────────────────────────────┐
│           VSCode Extension          │
│            (CommonJS)               │
│  ┌─────────────────────────────────┐│
│  │        Main Extension           ││
│  │     - extension.ts              ││
│  │     - services/                 ││
│  │     - handlers/                 ││
│  └─────────────────────────────────┘│
└─────────────────┬───────────────────┘
                  │ IPC Communication
                  │ (Worker Threads & Child Processes)
┌─────────────────▼───────────────────┐
│           Worker Processes          │
│             (ES Modules)            │
│  ┌─────────────────────────────────┐│
│  │     @xenova/transformers        ││
│  │     - pipeline()                ││
│  │     - feature-extraction        ││
│  │     - embeddings                ││
│  └─────────────────────────────────┘│
└─────────────────────────────────────┘
```

## Benefits

1. **VSCode Compatibility**: Main extension remains CommonJS
2. **ES Module Support**: Workers can use @xenova/transformers
3. **Clean Separation**: Clear boundary between CommonJS and ESM code
4. **Maintainable**: Each part uses its native module system
5. **Performance**: No dynamic import overhead in workers

## Usage

The hybrid setup is transparent to the extension's functionality. Workers are spawned as before:

```javascript
// Worker Threads (for search operations)
const worker = new Worker(workerPath, { workerData });

// Child Processes (for embedding creation)
const childProcess = fork(processPath, [], { env: { workerData } });
```

## Directory Structure

After build, the structure looks like:

```
dist/
├── extension.js              # Main extension (CommonJS)
├── extension.js.map
└── workers/                  # Worker processes (ES Modules)
    ├── package.json          # {"type": "module"}
    ├── codebase/
    │   ├── codebaseSearchWorker.js
    │   ├── codebaseEmbeddingProcess.js
    │   └── ...
    ├── confluence/
    │   ├── searchProcess.js
    │   ├── createEmbeddingForText.js
    │   └── ...
    ├── model/
    │   └── modelWorker.js
    └── utils/
        └── initializeEmbeddingModel.js
```

## Testing

The setup has been tested and verified to work with both:
- Worker Threads for search operations
- Child Processes for embedding creation

Both can successfully import and use @xenova/transformers while the main extension remains in CommonJS format.

## Build Process

1. **Pre-build**: Creates `dist/workers/package.json` with `{"type": "module"}`
2. **Extension Build**: Compiles main extension as CommonJS to `dist/`
3. **Workers Build**: Compiles workers as ES modules to `dist/workers/`
4. **Result**: Hybrid architecture ready for VSCode extension host
